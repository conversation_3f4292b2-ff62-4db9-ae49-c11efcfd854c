import { IsOptional, IsDateString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class CalendarQueryDto {
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> bắt đầu',
    example: '2025-06-01',
  })
  @IsOptional()
  @IsDateString({}, { message: '<PERSON><PERSON><PERSON> bắt đầu phải là chuỗi ngày hợp lệ' })
  start?: string;

  @ApiPropertyOptional({
    description: 'Ngày kết thúc',
    example: '2025-06-30',
  })
  @IsOptional()
  @IsDateString({}, { message: '<PERSON><PERSON><PERSON> kết thúc phải là chuỗi ngày hợp lệ' })
  end?: string;
}
